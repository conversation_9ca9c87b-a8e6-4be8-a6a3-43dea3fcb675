import request from '../request';

// 类型定义
interface SalesChannelOption {
    id: string;
    name: string;
    channelName: string;
    isRequired: boolean;
}

interface ProductSalesChannel {
    channelId: string;
    productCode: string;
}

interface ApiResponse<T> {
    data: T;
}

// 获取销售渠道列表
export const getSalesChannelList = (): Promise<ApiResponse<SalesChannelOption[]>> => {
    // 暂时返回模拟数据，实际项目中应该调用真实API
    return Promise.resolve({
        data: [
            { id: '1', name: '本来生活', channelName: '本来生活', isRequired: true },
            { id: '2', name: '卓望公司', channelName: '卓望公司', isRequired: false }
        ]
    });

    // 真实API调用（注释掉，等后端接口准备好后启用）
    // return request({
    //     url: '/sales-channel/list',
    //     method: 'get'
    // });
};

// 获取产品关联的销售渠道
export const getProductSalesChannels = (params: { productId: string }): Promise<ApiResponse<ProductSalesChannel[]>> => {
    // 模拟不同产品的回显数据
    const mockData: { [key: string]: ProductSalesChannel[] } = {
        // 产品1的销售渠道配置
        '1': [
            { channelId: '1', productCode: '92812917' },
            { channelId: '2', productCode: '' }
        ],
        // 产品2的销售渠道配置
        '2': [
            { channelId: '1', productCode: '92812918' }
        ],
        // 产品3的销售渠道配置
        '3': [
            { channelId: '2', productCode: 'ZW001' }
        ]
    };

    // 根据产品ID返回对应的模拟数据
    const productData = mockData[params.productId] || [];

    // 模拟网络延迟
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({
                data: productData
            });
        }, 300); // 300ms延迟模拟网络请求
    });

    // 真实API调用（注释掉，等后端接口准备好后启用）
    // return request({
    //     url: '/product/sales-channels',
    //     method: 'get',
    //     params
    // });
};

// 保存产品销售渠道配置
export const saveProductSalesChannels = (data: {
    productId: string;
    channels: Array<{
        id?: string;
        channelId: string;
        channelName: string;
        productCode: string;
        isRequired: boolean;
    }>;
}): Promise<ApiResponse<{ success: boolean }>> => {
    // 暂时返回模拟成功响应
    console.log('保存销售渠道配置:', data);
    return Promise.resolve({
        data: { success: true }
    });

    // 真实API调用（注释掉，等后端接口准备好后启用）
    // return request({
    //     url: '/product/sales-channels',
    //     method: 'post',
    //     data
    // });
};

// 验证渠道产品编码是否重复
export const validateChannelCode = (data: {
    channelId: string;
    productCode: string;
    productId: string;
}): Promise<ApiResponse<{ valid: boolean }>> => {
    // 暂时返回模拟验证结果（总是通过验证）
    console.log('验证渠道产品编码:', data);
    return Promise.resolve({
        data: { valid: true }
    });

    // 真实API调用（注释掉，等后端接口准备好后启用）
    // return request({
    //     url: '/product/validate-channel-code',
    //     method: 'post',
    //     data
    // });
};

// 删除产品销售渠道
export const deleteProductSalesChannel = (params: { id: string }): Promise<ApiResponse<{ success: boolean }>> => {
    return request({
        url: '/product/sales-channel',
        method: 'delete',
        params
    });
};
