import React, { useState, useEffect } from 'react';
import { Form, message, Row, Col } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import BaseModal from '@components/base-modal';
import BaseInput from '@components/base-input';
import BaseSelect from '@components/base-select';
import BaseButton from '@components/base-button';
import { useMutation, useQuery } from 'react-query';
import {
    getSalesChannelList,
    getProductSalesChannels,
    saveProductSalesChannels,
    validateChannelCode
} from '@services/sales-channel';
import { ReformChainError } from '@utils/errorCodeReform';
import styles from './sales-channel-modal.module.less';

interface SalesChannelModalProps {
    visible: boolean;
    onCancel: () => void;
    product: any;
    onSuccess: () => void;
}

interface SalesChannelItem {
    id?: string;
    channelId: string;
    channelName: string;
    productCode: string;
    isRequired: boolean;
}

const SalesChannelModal: React.FC<SalesChannelModalProps> = ({ visible, onCancel, product, onSuccess }) => {
    const [form] = Form.useForm();
    const [salesChannels, setSalesChannels] = useState<SalesChannelItem[]>([]);
    const [loading, setLoading] = useState(false);

    // 获取销售渠道选项
    const salesChannelOptionsQuery = useQuery(['salesChannelOptions'], getSalesChannelList, {
        onError(err: any) {
            ReformChainError(err);
        }
    });

    // 获取产品已关联的销售渠道
    const productSalesChannelsQuery = useQuery(
        ['productSalesChannels', product?.id],
        () => getProductSalesChannels({ productId: product?.id }),
        {
            enabled: !!product?.id && visible,
            onSuccess(data) {
                const channels = data?.data || [];
                setSalesChannels(channels.length > 0 ? channels : [createEmptyChannel()]);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    // 保存销售渠道配置
    const saveMutation = useMutation(saveProductSalesChannels, {
        onSuccess() {
            message.success('销售渠道维护成功');
            onSuccess();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    // 验证渠道产品编码
    const validateCodeMutation = useMutation(validateChannelCode, {
        onError(err: any) {
            ReformChainError(err);
        }
    });

    const createEmptyChannel = (): SalesChannelItem => ({
        channelId: '',
        channelName: '',
        productCode: '',
        isRequired: false
    });

    const salesChannelOptions = salesChannelOptionsQuery.data?.data || [];

    // 获取渠道配置信息
    const getChannelConfig = (channelId: string) => {
        const channel = salesChannelOptions.find((item: any) => item.id === channelId);
        const channelName = channel?.name || channel?.channelName || '';
        // 根据需求，本来生活需要填写产品编码，卓望公司不需要
        const isRequired = channelName === '本来生活';
        return {
            name: channelName,
            isRequired,
            placeholder: isRequired ? '请输入产品编码' : '当前销售渠道不需要填写产品编码'
        };
    };

    // 添加销售渠道
    const handleAddChannel = () => {
        setSalesChannels([...salesChannels, createEmptyChannel()]);
    };

    // 删除销售渠道
    const handleDeleteChannel = (index: number) => {
        const newChannels = salesChannels.filter((_, i) => i !== index);
        setSalesChannels(newChannels.length > 0 ? newChannels : [createEmptyChannel()]);
    };

    // 渠道选择变化
    const handleChannelChange = (index: number, channelId: string) => {
        const newChannels = [...salesChannels];
        const channelConfig = getChannelConfig(channelId);
        newChannels[index] = {
            ...newChannels[index],
            channelId,
            channelName: channelConfig.name,
            isRequired: channelConfig.isRequired,
            productCode: channelConfig.isRequired ? newChannels[index].productCode : ''
        };
        setSalesChannels(newChannels);
    };

    // 产品编码变化
    const handleProductCodeChange = (index: number, productCode: string) => {
        const newChannels = [...salesChannels];
        newChannels[index].productCode = productCode;
        setSalesChannels(newChannels);
    };

    // 验证表单
    const validateChannels = async () => {
        const errors: string[] = [];
        const usedCodes: { [key: string]: string[] } = {};

        for (let i = 0; i < salesChannels.length; i++) {
            const channel = salesChannels[i];

            if (!channel.channelId) {
                errors.push(`第${i + 1}行：请选择销售渠道`);
                continue;
            }

            if (channel.isRequired && !channel.productCode.trim()) {
                errors.push(`第${i + 1}行：${channel.channelName}渠道必须填写产品编码`);
                continue;
            }

            if (channel.productCode.trim()) {
                // 检查同一产品内的重复编码
                const channelKey = channel.channelId;
                if (!usedCodes[channelKey]) {
                    usedCodes[channelKey] = [];
                }

                if (usedCodes[channelKey].includes(channel.productCode)) {
                    errors.push(`当前产品在${channel.channelName}渠道已关联产品编码，请先删除原编码再添加`);
                    continue;
                }
                usedCodes[channelKey].push(channel.productCode);

                // 检查跨产品的重复编码
                try {
                    const validateResult = await validateCodeMutation.mutateAsync({
                        channelId: channel.channelId,
                        productCode: channel.productCode,
                        productId: product?.id
                    });

                    if (!validateResult.data.valid) {
                        errors.push(`当前产品在${channel.channelName}渠道已关联产品编码，请先删除原编码再添加`);
                    }
                } catch (error) {
                    // 验证接口出错时的处理
                    console.error('验证产品编码失败:', error);
                }
            }
        }

        return errors;
    };

    // 提交表单
    const handleSubmit = async () => {
        setLoading(true);
        try {
            const errors = await validateChannels();
            if (errors.length > 0) {
                message.error(errors[0]);
                return;
            }

            const validChannels = salesChannels.filter(
                (channel) => channel.channelId && (channel.productCode.trim() || !channel.isRequired)
            );

            await saveMutation.mutateAsync({
                productId: product?.id,
                channels: validChannels
            });
        } finally {
            setLoading(false);
        }
    };

    // 重置表单
    useEffect(() => {
        if (visible && product) {
            if (!productSalesChannelsQuery.data?.data?.length) {
                setSalesChannels([createEmptyChannel()]);
            }
        }
    }, [visible, product]);

    return (
        <BaseModal
            title='维护销售渠道信息'
            visible={visible}
            onCancelHandle={onCancel}
            okHandle={handleSubmit}
            okText='确定'
            width={600}
            className={styles.salesChannelModal}
        >
            <div className={styles.modalContent}>
                <div className={styles.header}>
                    <Row gutter={16}>
                        <Col span={8}>
                            <span className={styles.label}>销售渠道</span>
                        </Col>
                        <Col span={12}>
                            <span className={styles.label}>渠道产品编码</span>
                        </Col>
                        <Col span={4}>
                            <span className={styles.label}>操作</span>
                        </Col>
                    </Row>
                </div>

                <div className={styles.channelList}>
                    {salesChannels.map((channel, index) => (
                        <Row key={index} gutter={16} className={styles.channelRow}>
                            <Col span={8}>
                                <BaseSelect
                                    placeholder='请选择'
                                    value={channel.channelId}
                                    onChange={(value) => handleChannelChange(index, value)}
                                    options={salesChannelOptions.map((item: any) => ({
                                        label: item.name || item.channelName,
                                        value: item.id || item.channelName
                                    }))}
                                />
                            </Col>
                            <Col span={12}>
                                <div>
                                    <BaseInput
                                        placeholder={getChannelConfig(channel.channelId).placeholder}
                                        value={channel.productCode}
                                        onChange={(e) => handleProductCodeChange(index, e.target.value)}
                                        disabled={!channel.isRequired && !!channel.channelId}
                                    />
                                    {!channel.isRequired && channel.channelId && (
                                        <div className={styles.tip}>当前销售渠道不需要填写产品编码</div>
                                    )}
                                </div>
                            </Col>
                            <Col span={4}>
                                <BaseButton
                                    type='text'
                                    icon={<DeleteOutlined />}
                                    onClick={() => handleDeleteChannel(index)}
                                    className={styles.deleteBtn}
                                />
                            </Col>
                        </Row>
                    ))}
                </div>

                <div className={styles.footer}>
                    <BaseButton
                        type='dashed'
                        icon={<PlusOutlined />}
                        onClick={handleAddChannel}
                        className={styles.addBtn}
                    >
                        添加渠道
                    </BaseButton>
                </div>
            </div>
        </BaseModal>
    );
};

export default SalesChannelModal;
