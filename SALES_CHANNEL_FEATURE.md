# 销售渠道维护功能

## 功能概述

为产品列表页面增加了"维护销售渠道"功能，允许用户为每个产品配置第三方销售渠道的产品编码信息。

## 功能特性

### 1. 基本功能
- 点击产品列表中的"维护销售渠道"按钮，打开销售渠道维护弹窗
- 支持添加、修改、删除销售渠道配置
- 支持多个销售渠道的配置

### 2. 渠道规则
- **本来生活渠道**：必须填写产品编码
- **卓望公司渠道**：不需要填写产品编码，输入框会被禁用并显示提示信息

### 3. 验证规则
- 同一产品的同一销售渠道不能有重复的产品编码
- 不同产品之间的同一销售渠道不能使用相同的产品编码
- 违反规则时会显示相应的错误提示

### 4. 用户界面
- 弹窗采用系统统一的样式风格
- 表格形式展示：销售渠道 | 渠道产品编码 | 操作
- 删除按钮位于每行最右侧
- 底部有"添加渠道"按钮

## 文件结构

```
src/pages/food-list/
├── food-list.tsx                    # 主页面文件（已修改）
├── sales-channel-modal.tsx         # 销售渠道维护弹窗组件（新增）
└── sales-channel-modal.module.less # 弹窗样式文件（新增）

src/services/sales-channel/
└── index.ts                        # 销售渠道相关API服务（新增）
```

## 主要组件

### SalesChannelModal 组件

**Props:**
- `visible: boolean` - 弹窗显示状态
- `onCancel: () => void` - 取消回调
- `product: any` - 当前产品信息
- `onSuccess: () => void` - 成功回调

**主要功能:**
- 获取销售渠道选项列表
- 获取产品已关联的销售渠道
- 验证产品编码重复性
- 保存销售渠道配置

## API 接口

### 1. 获取销售渠道列表
```typescript
getSalesChannelList(): Promise<{
  data: Array<{
    id: string;
    name: string;
    channelName: string;
    isRequired: boolean;
  }>
}>
```

### 2. 获取产品关联的销售渠道
```typescript
getProductSalesChannels(params: { 
  productId: string 
}): Promise<{
  data: Array<SalesChannelItem>
}>
```

### 3. 保存产品销售渠道配置
```typescript
saveProductSalesChannels(data: {
  productId: string;
  channels: Array<SalesChannelItem>;
}): Promise<{ data: { success: boolean } }>
```

### 4. 验证渠道产品编码
```typescript
validateChannelCode(data: {
  channelId: string;
  productCode: string;
  productId: string;
}): Promise<{ data: { valid: boolean } }>
```

## 使用方法

1. 在产品列表页面，找到需要维护销售渠道的产品
2. 点击该产品行的"维护销售渠道"按钮
3. 在弹出的弹窗中：
   - 选择销售渠道
   - 根据渠道要求填写或不填写产品编码
   - 可以添加多个销售渠道配置
   - 可以删除不需要的配置
4. 点击"确定"保存配置

## 错误提示

- `"第X行：请选择销售渠道"` - 未选择销售渠道
- `"第X行：本来生活渠道必须填写产品编码"` - 本来生活渠道未填写产品编码
- `"当前产品在XX渠道已关联产品编码，请先删除原编码再添加"` - 产品编码重复

## 注意事项

1. 当前使用模拟数据，实际部署时需要：
   - 启用真实的API调用（取消注释）
   - 配置正确的后端接口地址
   - 根据实际数据结构调整接口参数

2. 销售渠道选项目前硬编码为"本来生活"和"卓望公司"，可根据实际需求调整

3. 样式采用CSS Modules，确保样式隔离

## 扩展建议

1. 可以考虑添加批量操作功能
2. 可以添加导入/导出功能
3. 可以添加操作日志记录
4. 可以添加更多的验证规则
