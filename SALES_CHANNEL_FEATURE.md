# 销售渠道维护功能

## 功能概述

为产品列表页面增加了"维护销售渠道"功能，允许用户为每个产品配置第三方销售渠道的产品编码信息。使用了 Antd 的动态表单（Form.List）实现。

## 功能特性

### 1. 基本功能

-   点击产品列表中的"维护销售渠道"按钮，打开销售渠道维护弹窗
-   支持动态添加、删除销售渠道配置
-   支持多个销售渠道的配置

### 2. 渠道规则

-   **本来生活渠道**：必须填写产品编码（表单验证）
-   **卓望公司渠道**：不需要填写产品编码，但可以选择填写

### 3. 验证规则

-   使用 Antd Form 的内置验证机制
-   本来生活渠道的产品编码为必填项
-   同一产品的同一销售渠道不能有重复的产品编码
-   不同产品之间的同一销售渠道不能使用相同的产品编码
-   违反规则时会显示相应的错误提示

### 4. 用户界面

-   使用动态表单布局，每行包含：销售渠道选择器 | 产品编码输入框 | 删除按钮
-   卓望公司渠道会显示提示："当前销售渠道不需要填写产品编码"
-   底部有"添加销售渠道"按钮
-   采用系统统一的弹窗样式

## 文件结构

```
src/pages/food-list/
├── food-list.tsx                    # 主页面文件（已修改）
├── sales-channel-modal.tsx         # 销售渠道维护弹窗组件（新增）
└── sales-channel-modal.module.less # 弹窗样式文件（新增）

src/services/sales-channel/
└── index.ts                        # 销售渠道相关API服务（新增）
```

## 主要组件

### SalesChannelModal 组件

**Props:**

-   `visible: boolean` - 弹窗显示状态
-   `onCancel: () => void` - 取消回调
-   `product: any` - 当前产品信息
-   `onSuccess: () => void` - 成功回调

**主要功能:**

-   使用 Antd Form.List 实现动态表单
-   获取销售渠道选项列表（目前使用硬编码数据）
-   获取产品已关联的销售渠道
-   表单验证和提交
-   验证产品编码重复性
-   保存销售渠道配置

**技术特点:**

-   使用 `Form.List` 实现动态增减表单项
-   使用 `shouldUpdate` 实现表单项之间的联动
-   使用 `MinusCircleOutlined` 和 `PlusOutlined` 图标
-   集成 react-query 进行数据管理

## API 接口

### 1. 获取销售渠道列表

```typescript
getSalesChannelList(): Promise<{
  data: Array<{
    id: string;
    name: string;
    channelName: string;
    isRequired: boolean;
  }>
}>
```

### 2. 获取产品关联的销售渠道

```typescript
getProductSalesChannels(params: {
  productId: string
}): Promise<{
  data: Array<SalesChannelItem>
}>
```

### 3. 保存产品销售渠道配置

```typescript
saveProductSalesChannels(data: {
  productId: string;
  channels: Array<SalesChannelItem>;
}): Promise<{ data: { success: boolean } }>
```

### 4. 验证渠道产品编码

```typescript
validateChannelCode(data: {
  channelId: string;
  productCode: string;
  productId: string;
}): Promise<{ data: { valid: boolean } }>
```

## 使用方法

1. 在产品列表页面，找到需要维护销售渠道的产品
2. 点击该产品行的"维护销售渠道"按钮
3. 在弹出的弹窗中：
    - 选择销售渠道（本来生活/卓望公司）
    - 填写产品编码：
        - 本来生活：必须填写（红色必填标识）
        - 卓望公司：可选填写，会显示提示信息
    - 点击"添加销售渠道"可以添加更多配置
    - 点击行末的删除图标可以删除配置
4. 点击"确定"保存配置，系统会进行验证
5. 如有错误会显示具体的错误信息

## 错误提示

-   `"请选择销售渠道"` - 未选择销售渠道（Antd Form 验证）
-   `"本来生活渠道必须填写产品编码"` - 本来生活渠道未填写产品编码（Antd Form 验证）
-   `"当前产品在XX渠道已关联产品编码，请先删除原编码再添加"` - 产品编码重复（自定义验证）

## 注意事项

1. **数据模拟**：当前使用模拟数据，实际部署时需要：

    - 启用真实的 API 调用（取消注释）
    - 配置正确的后端接口地址
    - 根据实际数据结构调整接口参数

2. **销售渠道配置**：销售渠道选项目前硬编码为"本来生活"和"卓望公司"，可根据实际需求调整

3. **样式处理**：删除了原来的 CSS Modules 样式文件，直接使用内联样式和 Antd 默认样式

4. **表单验证**：使用 Antd Form 的内置验证机制，确保数据完整性

5. **图标使用**：使用了 Antd 官方推荐的`MinusCircleOutlined`和`PlusOutlined`图标

## 扩展建议

1. 可以考虑添加批量操作功能
2. 可以添加导入/导出功能
3. 可以添加操作日志记录
4. 可以添加更多的验证规则
